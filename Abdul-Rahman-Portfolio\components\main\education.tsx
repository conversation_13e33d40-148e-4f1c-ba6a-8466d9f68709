"use client";

import { motion } from "framer-motion";
import { slideInFromLeft, slideInFromRight } from "@/lib/motion";

export const Education = () => {
  return (
    <section
      id="education"
      className="flex flex-col items-center justify-center py-20 px-10"
    >
      <motion.h1
        initial={{ opacity: 0, y: -50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        className="text-[40px] font-semibold text-transparent bg-clip-text bg-gradient-to-r from-purple-500 to-cyan-500 py-10"
      >
        Education
      </motion.h1>

      <div className="w-full max-w-4xl space-y-8">
        {/* Current Education */}
        <motion.div
          variants={slideInFromLeft(0.5)}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="bg-[#0C0C0C] border border-[#2A0E61] rounded-lg p-6 shadow-lg"
        >
          <div className="flex flex-col md:flex-row md:justify-between md:items-start">
            <div className="flex-1">
              <h3 className="text-2xl font-bold text-white mb-2">
                Bachelor of Technology in Computer Science and Engineering
              </h3>
              <p className="text-lg text-purple-400 mb-2">
                JNTUA College of Engineering Pulivendula
              </p>
              <p className="text-gray-300 mb-4">
                Currently pursuing B.Tech with focus on Machine Learning and AI
              </p>
              <div className="flex flex-wrap gap-2">
                <span className="px-3 py-1 bg-purple-600/20 text-purple-300 rounded-full text-sm">
                  Computer Science
                </span>
                <span className="px-3 py-1 bg-cyan-600/20 text-cyan-300 rounded-full text-sm">
                  Machine Learning
                </span>
                <span className="px-3 py-1 bg-blue-600/20 text-blue-300 rounded-full text-sm">
                  AI & Data Science
                </span>
              </div>
            </div>
            <div className="mt-4 md:mt-0 md:ml-6">
              <p className="text-gray-400 text-sm">2022 - 2026</p>
              <p className="text-green-400 font-semibold">CGPA: 8.0</p>
            </div>
          </div>
        </motion.div>

        {/* Intermediate Education */}
        <motion.div
          variants={slideInFromRight(0.5)}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="bg-[#0C0C0C] border border-[#2A0E61] rounded-lg p-6 shadow-lg"
        >
          <div className="flex flex-col md:flex-row md:justify-between md:items-start">
            <div className="flex-1">
              <h3 className="text-2xl font-bold text-white mb-2">
                Intermediate
              </h3>
              <p className="text-lg text-purple-400 mb-2">
                Narayana Junior College, Gudur
              </p>
              <p className="text-gray-300 mb-4">
                Completed intermediate education with focus on Mathematics, Physics, and Chemistry
              </p>
              <div className="flex flex-wrap gap-2">
                <span className="px-3 py-1 bg-purple-600/20 text-purple-300 rounded-full text-sm">
                  Mathematics
                </span>
                <span className="px-3 py-1 bg-cyan-600/20 text-cyan-300 rounded-full text-sm">
                  Physics
                </span>
                <span className="px-3 py-1 bg-blue-600/20 text-blue-300 rounded-full text-sm">
                  Chemistry
                </span>
              </div>
            </div>
            <div className="mt-4 md:mt-0 md:ml-6">
              <p className="text-gray-400 text-sm">2020 - 2022</p>
              <p className="text-green-400 font-semibold">CGPA: 9</p>
            </div>
          </div>
        </motion.div>

        {/* Secondary School */}
        <motion.div
          variants={slideInFromLeft(0.7)}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="bg-[#0C0C0C] border border-[#2A0E61] rounded-lg p-6 shadow-lg"
        >
          <div className="flex flex-col md:flex-row md:justify-between md:items-start">
            <div className="flex-1">
              <h3 className="text-2xl font-bold text-white mb-2">
                Secondary School
              </h3>
              <p className="text-lg text-purple-400 mb-2">
                Prospero English Medium High School
              </p>
              <p className="text-gray-300 mb-4">
                Completed secondary education with strong foundation in core subjects
              </p>
              <div className="flex flex-wrap gap-2">
                <span className="px-3 py-1 bg-purple-600/20 text-purple-300 rounded-full text-sm">
                  English
                </span>
                <span className="px-3 py-1 bg-cyan-600/20 text-cyan-300 rounded-full text-sm">
                  Mathematics
                </span>
                <span className="px-3 py-1 bg-blue-600/20 text-blue-300 rounded-full text-sm">
                  Science
                </span>
              </div>
            </div>
            <div className="mt-4 md:mt-0 md:ml-6">
              <p className="text-gray-400 text-sm">2019 - 2020</p>
              <p className="text-green-400 font-semibold">CGPA: 9.4</p>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};
