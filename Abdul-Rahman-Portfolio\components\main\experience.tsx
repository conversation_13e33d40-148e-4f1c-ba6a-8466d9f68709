"use client";

import { motion } from "framer-motion";
import { slideInFromLeft, slideInFromRight } from "@/lib/motion";

export const Experience = () => {
  return (
    <section
      id="experience"
      className="flex flex-col items-center justify-center py-20 px-10"
    >
      <motion.h1
        initial={{ opacity: 0, y: -50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        className="text-[40px] font-semibold text-transparent bg-clip-text bg-gradient-to-r from-purple-500 to-cyan-500 py-10"
      >
        Experience
      </motion.h1>

      <div className="w-full max-w-4xl">
        <motion.div
          variants={slideInFromLeft(0.5)}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="bg-[#0C0C0C] border border-[#2A0E61] rounded-lg p-6 shadow-lg"
        >
          <div className="flex flex-col md:flex-row md:justify-between md:items-start">
            <div className="flex-1">
              <h3 className="text-2xl font-bold text-white mb-2">
                Machine Learning Intern
              </h3>
              <p className="text-lg text-purple-400 mb-2">
                CODTECH IT SOLUTIONS
              </p>
              <p className="text-gray-400 text-sm mb-4">
                September 2024 - November 2024
              </p>
              
              <div className="space-y-3 mb-6">
                <div className="flex items-start">
                  <span className="text-cyan-400 mr-2">•</span>
                  <p className="text-gray-300">
                    Built ML solutions for real-world problems using Python and advanced machine learning algorithms
                  </p>
                </div>
                <div className="flex items-start">
                  <span className="text-cyan-400 mr-2">•</span>
                  <p className="text-gray-300">
                    Worked with the team under Neela Santhosh Kumar's mentorship to develop and deploy ML models
                  </p>
                </div>
                <div className="flex items-start">
                  <span className="text-cyan-400 mr-2">•</span>
                  <p className="text-gray-300">
                    Handled data preprocessing, model training, and deployment phases of ML projects
                  </p>
                </div>
                <div className="flex items-start">
                  <span className="text-cyan-400 mr-2">•</span>
                  <p className="text-gray-300">
                    Enhanced ML skills and gained valuable industry exposure in machine learning development
                  </p>
                </div>
              </div>

              <div className="flex flex-wrap gap-2">
                <span className="px-3 py-1 bg-purple-600/20 text-purple-300 rounded-full text-sm">
                  Python
                </span>
                <span className="px-3 py-1 bg-cyan-600/20 text-cyan-300 rounded-full text-sm">
                  Machine Learning
                </span>
                <span className="px-3 py-1 bg-blue-600/20 text-blue-300 rounded-full text-sm">
                  Data Preprocessing
                </span>
                <span className="px-3 py-1 bg-green-600/20 text-green-300 rounded-full text-sm">
                  Model Deployment
                </span>
                <span className="px-3 py-1 bg-yellow-600/20 text-yellow-300 rounded-full text-sm">
                  Team Collaboration
                </span>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Certifications Section */}
        <motion.div
          variants={slideInFromRight(0.7)}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="mt-8 bg-[#0C0C0C] border border-[#2A0E61] rounded-lg p-6 shadow-lg"
        >
          <h3 className="text-2xl font-bold text-white mb-6">Certifications</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-[#1A1A1A] rounded-lg p-4 border border-[#2A0E61]">
              <h4 className="text-lg font-semibold text-purple-400 mb-2">Python Basics for Data Science</h4>
              <p className="text-gray-400 text-sm">IBM</p>
            </div>
            
            <div className="bg-[#1A1A1A] rounded-lg p-4 border border-[#2A0E61]">
              <h4 className="text-lg font-semibold text-purple-400 mb-2">Analysing Data with Python</h4>
              <p className="text-gray-400 text-sm">IBM</p>
            </div>
            
            <div className="bg-[#1A1A1A] rounded-lg p-4 border border-[#2A0E61]">
              <h4 className="text-lg font-semibold text-purple-400 mb-2">Visualizing Data with Python</h4>
              <p className="text-gray-400 text-sm">IBM</p>
            </div>
            
            <div className="bg-[#1A1A1A] rounded-lg p-4 border border-[#2A0E61]">
              <h4 className="text-lg font-semibold text-purple-400 mb-2">SQL for Data Science</h4>
              <p className="text-gray-400 text-sm">IBM</p>
            </div>
            
            <div className="bg-[#1A1A1A] rounded-lg p-4 border border-[#2A0E61]">
              <h4 className="text-lg font-semibold text-purple-400 mb-2">Python for Data Science Project</h4>
              <p className="text-gray-400 text-sm">IBM (edX)</p>
            </div>
            
            <div className="bg-[#1A1A1A] rounded-lg p-4 border border-[#2A0E61]">
              <h4 className="text-lg font-semibold text-purple-400 mb-2">Math for Machine Learning with Python</h4>
              <p className="text-gray-400 text-sm">edX</p>
            </div>
            
            <div className="bg-[#1A1A1A] rounded-lg p-4 border border-[#2A0E61]">
              <h4 className="text-lg font-semibold text-purple-400 mb-2">Machine Learning with Python: Practical Introduction</h4>
              <p className="text-gray-400 text-sm">IBM (edX) | NPTEL</p>
            </div>
            
            <div className="bg-[#1A1A1A] rounded-lg p-4 border border-[#2A0E61]">
              <h4 className="text-lg font-semibold text-purple-400 mb-2">Introduction to ML</h4>
              <p className="text-gray-400 text-sm">NPTEL</p>
            </div>
            
            <div className="bg-[#1A1A1A] rounded-lg p-4 border border-[#2A0E61]">
              <h4 className="text-lg font-semibold text-purple-400 mb-2">Deep Learning with TensorFlow</h4>
              <p className="text-gray-400 text-sm">IBM (edX)</p>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};
