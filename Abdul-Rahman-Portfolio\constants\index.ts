import { FaYoutube, FaFacebook } from "react-icons/fa";
import {
  Rx<PERSON><PERSON><PERSON><PERSON><PERSON>,
  Rx<PERSON><PERSON><PERSON><PERSON><PERSON>,
  Rx<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  RxTwitter<PERSON><PERSON>,
  Rx<PERSON><PERSON>edin<PERSON>ogo,
  RxEnvelopeClosed,
} from "react-icons/rx";

export const SKILL_DATA = [
  {
    skill_name: "Python",
    image: "python.svg",
    width: 80,
    height: 80,
  },
  {
    skill_name: "Scikit-learn",
    image: "sklearn.svg",
    width: 80,
    height: 80,
  },
  {
    skill_name: "TensorFlow",
    image: "tensorflow.svg",
    width: 80,
    height: 80,
  },
  {
    skill_name: "Keras",
    image: "keras.svg",
    width: 80,
    height: 80,
  },
  {
    skill_name: "Pandas",
    image: "pandas.svg",
    width: 80,
    height: 80,
  },
  {
    skill_name: "NumPy",
    image: "numpy.svg",
    width: 80,
    height: 80,
  },
  {
    skill_name: "<PERSON><PERSON>lotli<PERSON>",
    image: "matplotlib.svg",
    width: 80,
    height: 80,
  },
  {
    skill_name: "Seaborn",
    image: "seaborn.svg",
    width: 80,
    height: 80,
  },
  {
    skill_name: "Plotly",
    image: "plotly.svg",
    width: 80,
    height: 80,
  },
  {
    skill_name: "Streamlit",
    image: "streamlit.svg",
    width: 80,
    height: 80,
  },
  {
    skill_name: "Flask",
    image: "flask.svg",
    width: 80,
    height: 80,
  },
  {
    skill_name: "MySQL",
    image: "mysql.png",
    width: 80,
    height: 80,
  },
  {
    skill_name: "MongoDB",
    image: "mongodb.png",
    width: 40,
    height: 40,
  },
] as const;

export const SOCIALS = [
  {
    name: "LinkedIn",
    icon: RxLinkedinLogo,
    link: "https://linkedin.com/in/irakam-siva", // Update with actual LinkedIn URL
  },
  {
    name: "GitHub",
    icon: RxGithubLogo,
    link: "https://github.com/irakamsiva", // Update with actual GitHub URL
  },
  {
    name: "Email",
    icon: RxEnvelopeClosed,
    link: "mailto:<EMAIL>",
  },
] as const;

export const FRONTEND_SKILL = [
  {
    skill_name: "Python",
    image: "python.svg",
    width: 80,
    height: 80,
  },
  {
    skill_name: "Scikit-learn",
    image: "sklearn.svg",
    width: 80,
    height: 80,
  },
  {
    skill_name: "Pandas",
    image: "pandas.svg",
    width: 80,
    height: 80,
  },
  {
    skill_name: "NumPy",
    image: "numpy.svg",
    width: 80,
    height: 80,
  },
  {
    skill_name: "Matplotlib",
    image: "matplotlib.svg",
    width: 80,
    height: 80,
  },
  {
    skill_name: "Seaborn",
    image: "seaborn.svg",
    width: 80,
    height: 80,
  },
] as const;

export const BACKEND_SKILL = [
  {
    skill_name: "TensorFlow",
    image: "tensorflow.svg",
    width: 80,
    height: 80,
  },
  {
    skill_name: "Keras",
    image: "keras.svg",
    width: 80,
    height: 80,
  },
  {
    skill_name: "Flask",
    image: "flask.svg",
    width: 80,
    height: 80,
  },
  {
    skill_name: "Streamlit",
    image: "streamlit.svg",
    width: 80,
    height: 80,
  },
] as const;

export const FULLSTACK_SKILL = [
  {
    skill_name: "MySQL",
    image: "mysql.png",
    width: 80,
    height: 80,
  },
  {
    skill_name: "MongoDB",
    image: "mongodb.png",
    width: 40,
    height: 40,
  },
  {
    skill_name: "Plotly",
    image: "plotly.svg",
    width: 80,
    height: 80,
  },
] as const;

export const OTHER_SKILL = [
  {
    skill_name: "Machine Learning",
    image: "ml.svg",
    width: 80,
    height: 80,
  },
  {
    skill_name: "Deep Learning",
    image: "dl.svg",
    width: 80,
    height: 80,
  },
  {
    skill_name: "NLP",
    image: "nlp.svg",
    width: 80,
    height: 80,
  },
] as const;

export const PROJECTS = [
  {
    title: "📰 News Analyser - Intelligent News Processing System",
    description: "Advanced NLP system that ingests news from multiple sources (Google & Bing) with intelligent fallback scraping for 100% article coverage. Features 5 REST endpoints with CORS, input validation & API-key authentication. Implements 3 NLP pipelines: VADER + DistilBERT sentiment analysis, BART summarization, TF-IDF & spaCy NLR topic extraction. Built with Streamlit dashboard featuring bilingual (English & Hindi) TTS and real-time Plotly visualizations.",
    image: "/projects/news-analyser.svg",
    link: "https://github.com/irakamsiva/news-analyser", // Update with actual GitHub URL
  },
  {
    title: "🛡️ FraudShield - Advanced Fraud Detection System",
    description: "Comprehensive fraud detection system with 3-service microservices ecosystem using Docker-Compose (76% Jupyter notebooks, 17% Python scripts, 7% HTML). Deployed scikit-learn fraud model via /score endpoint with Low (0-49%), Medium (50-79%), High (80-100%) tiers. Implemented FAST APIs with CORS, input validation, and API-key authentication for secure, scalable fraud detection. Built responsive JS/HTML frontend with real-time risk visualization, local-storage history, and dark-mode support.",
    image: "/projects/fraudshield.svg",
    link: "https://github.com/irakamsiva/fraudshield", // Update with actual GitHub URL
  },
  {
    title: "📈 NVIDIA Stock Prediction - ML-Powered Financial Forecasting",
    description: "Advanced 4-phase NVIDIA stock prediction pipeline in a single Jupyter Notebook. Developed Linear Regression forecasting model: R²=0.99, RMSE=0.34, MAE=0.18. Implemented Linear Regression, ARIMA & LSTM models using Scikit-learn & TensorFlow. Utilized 7 Python libraries (Pandas, NumPy, Matplotlib, Seaborn, Scikit-learn, TensorFlow/Keras) for comprehensive data analysis and visualization.",
    image: "/projects/nvidia-stock.svg",
    link: "https://github.com/irakamsiva/nvidia-stock-prediction", // Update with actual GitHub URL
  },
] as const;

export const FOOTER_DATA = [
  {
    title: "Community",
    data: [
      {
        name: "GitHub",
        icon: RxGithubLogo,
        link: "https://github.com/irakamsiva", // Update with actual GitHub URL
      },
    ],
  },
  {
    title: "Social Media",
    data: [
      {
        name: "LinkedIn",
        icon: RxLinkedinLogo,
        link: "https://linkedin.com/in/irakam-siva", // Update with actual LinkedIn URL
      },
    ],
  },
  {
    title: "Contact",
    data: [
      {
        name: "Email Me",
        icon: RxEnvelopeClosed,
        link: "mailto:<EMAIL>",
      },
      {
        name: "Phone",
        icon: null,
        link: "tel:+918977899897",
      },
      {
        name: "Location",
        icon: null,
        link: "#",
      },
    ],
  },
] as const;

export const NAV_LINKS = [
  {
    title: "About me",
    link: "#about-me",
  },
  {
    title: "Skills",
    link: "#skills",
  },
  {
    title: "Experience",
    link: "#experience",
  },
  {
    title: "Education",
    link: "#education",
  },
  {
    title: "Projects",
    link: "#projects",
  },
] as const;


